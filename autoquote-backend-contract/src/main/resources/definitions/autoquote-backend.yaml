openapi: 3.1.0
info:
  title: Autoquote-Backend
  description: Autoquote Backend Rest for IRCA
  version: "1"
servers:
- url: http://localhost:8080
  description: Generated server url
paths:
  /quickquote/v2/quotes/{uuid}/rates:
    put:
      tags:
      - offer-controller
      summary: Get the rate quote
      operationId: rateQuotes
      parameters:
      - name: uuid
        in: path
        required: true
        schema:
          type: string
      - name: api<PERSON>ey
        in: query
        required: true
        schema:
          type: string
      - name: province
        in: query
        required: true
        schema:
          type: string
      - name: language
        in: query
        required: true
        schema:
          type: string
      - name: subBroker
        in: query
        required: false
        schema:
          type: string
      - name: organizationSource
        in: query
        required: false
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/QuoteDTO"
        required: true
      responses:
        "200":
          description: Successfully retrieved ResponseEntity
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResponseDTO"
        "401":
          description: You are not authorized to view the resource
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResponseDTO"
        "403":
          description: Accessing the resource you were trying to reach is forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResponseDTO"
        "404":
          description: The resource you were trying to reach is not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResponseDTO"
  /quickquote/v2/quotes/rates:
    post:
      tags:
      - offer-controller
      summary: Get the rate quote
      operationId: rateQuote
      parameters:
      - name: actionToken
        in: query
        required: true
        schema:
          type: string
      - name: apiKey
        in: query
        required: true
        schema:
          type: string
      - name: province
        in: query
        required: true
        schema:
          type: string
      - name: language
        in: query
        required: true
        schema:
          type: string
      - name: subBroker
        in: query
        required: false
        schema:
          type: string
      - name: organizationSource
        in: query
        required: false
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/QuoteDTO"
        required: true
      responses:
        "200":
          description: Successfully retrieved ResponseEntity
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResponseDTO"
        "401":
          description: You are not authorized to view the resource
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResponseDTO"
        "403":
          description: Accessing the resource you were trying to reach is forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResponseDTO"
        "404":
          description: The resource you were trying to reach is not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ResponseDTO"
  /quickquote/v2/vehicles/{year}/{makeId}/models:
    get:
      tags:
      - vehicle-controller
      summary: Get the vehicule models
      operationId: getVehicleModels
      parameters:
      - name: year
        in: path
        required: true
        schema:
          type: string
      - name: makeId
        in: path
        required: true
        schema:
          type: string
      - name: province
        in: query
        required: true
        schema:
          type: string
      - name: language
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: Successfully retrieved a list of vehicule models
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ModelDTO"
        "401":
          description: You are not authorized to view the resource
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ModelDTO"
        "403":
          description: Accessing the resource you were trying to reach is forbidden
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ModelDTO"
        "404":
          description: The resource you were trying to reach is not found
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ModelDTO"
  /quickquote/v2/vehicle/{year}/makes:
    get:
      tags:
      - vehicle-controller
      summary: Get the vehicle makes
      operationId: getVehicleMakeList
      parameters:
      - name: province
        in: query
        required: true
        schema:
          type: string
      - name: language
        in: query
        required: true
        schema:
          type: string
      - name: year
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: Successfully retrieved a list of vehicule maker
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MakeDTO"
        "401":
          description: You are not authorized to view the resource
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MakeDTO"
        "403":
          description: Accessing the resource you were trying to reach is forbidden
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MakeDTO"
        "404":
          description: The resource you were trying to reach is not found
          content:
            '*/*':
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MakeDTO"
  /quickquote/v2/distributors/{subBrokerNo}:
    get:
      tags:
      - distributor-controller
      summary: distributor
      description: Retrieve broker information based on subBroker number
      operationId: retrieveBrokerInfo
      parameters:
      - name: apiKey
        in: query
        required: true
        schema:
          type: string
      - name: language
        in: query
        required: true
        schema:
          type: string
      - name: province
        in: query
        required: true
        schema:
          type: string
      - name: postalCode
        in: query
        required: false
        schema:
          type: string
      - name: subBrokerNo
        in: path
        required: true
        schema:
          type: string
      - name: origin
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: Successfully retrieved DistributorDTO
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DistributorDTO"
        "401":
          description: You are not authorized to view the resource
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DistributorDTO"
        "403":
          description: Accessing the resource you were trying to reach is forbidden
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DistributorDTO"
        "404":
          description: The resource you were trying to reach is not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DistributorDTO"
components:
  schemas:
    AddressDTO:
      type: object
      properties:
        postalCode:
          type: string
        municipalityCode:
          type: string
        addressLine:
          type: string
        city:
          type: string
        civicNbr:
          type: string
        country:
          type: string
        floor:
          type: string
        province:
          type: string
        street:
          type: string
        streetDirection:
          type: string
        streetType:
          type: string
        suiteNbr:
          type: string
    ClaimDTO:
      type: object
      properties:
        claimSequence:
          type: string
        dateOfLoss:
          type: string
        nature:
          type: string
          enum:
          - AA
          - NA
          - AAF
          - AWP
          - SWD
          - HR
          - FV
          - GR
          - GW
          - TH
          - WN
          - AP
          - AB
          - BI
          - FIR
          - VAN
          - IA
          - ONC
    ConsentDTO:
      type: object
      properties:
        consentInd:
          type: boolean
        consentType:
          type: string
          enum:
          - PROFILE
          - MARKETING
          - CREDIT_SCORE
    ConvictionDTO:
      type: object
      properties:
        convictionSequence:
          type: string
        nbYearsOld:
          type: string
        type:
          type: string
          enum:
          - MAJ
          - MIN
          - DIS
    CoverageDTO:
      type: object
      properties:
        code:
          type: string
        description:
          type: string
        selectedInd:
          type: boolean
        selectableInd:
          type: boolean
        eligibleInd:
          type: boolean
        coverageValues:
          type: array
          items:
            $ref: "#/components/schemas/CoverageValueDTO"
        ihvCode:
          type: string
    CoverageValueDTO:
      type: object
      properties:
        amount:
          type: integer
          format: int32
        coverageValueType:
          type: string
          enum:
          - LIMIT_AMOUNT
          - DEDUCTIBLE_AMOUNT
          - LIMIT_MULTIL_DEATH_AMOUNT
          - LIMIT_MED_EXPENSES_AMOUNT
        selectedInd:
          type: boolean
    DriverDTO:
      type: object
      properties:
        dateOfBirth:
          type: string
          format: date
        firstName:
          type: string
        lastName:
          type: string
        gender:
          type: string
        partyType:
          type: string
          enum:
          - "[code=PERSON]"
          - "[code=COMPANY]"
          - "[code=DRIVER]"
        unstructuredName:
          type: string
        id:
          type: integer
          format: int32
        address:
          $ref: "#/components/schemas/AddressDTO"
        emailAddress:
          type: string
        phoneNumber:
          type: string
        partyRoles:
          type: array
          items:
            $ref: "#/components/schemas/PartyRoleDTO"
        consents:
          type: array
          items:
            $ref: "#/components/schemas/ConsentDTO"
        principalInsuredSinceCode:
          type: string
        numberOfMinorInfractions:
          type: integer
          format: int32
        numberOfMajorInfractions:
          type: integer
          format: int32
        numberOfRelevantClaims:
          type: integer
          format: int32
        interestedByUbiInd:
          type: boolean
        partyId:
          type: integer
          format: int32
        licenseNbr:
          type: string
        driverLicenseType:
          type: string
        licenseObtentionDate:
          type: string
          format: date
        claims:
          type: array
          items:
            $ref: "#/components/schemas/ClaimDTO"
        convictions:
          type: array
          items:
            $ref: "#/components/schemas/ConvictionDTO"
    OfferDTO:
      type: object
      properties:
        offerCode:
          type: string
        selectedInd:
          type: boolean
        annualPremium:
          type: number
        monthlyPremium:
          type: number
        coverages:
          type: array
          items:
            $ref: "#/components/schemas/CoverageDTO"
    PartyDTO:
      type: object
      properties:
        dateOfBirth:
          type: string
          format: date
        firstName:
          type: string
        lastName:
          type: string
        gender:
          type: string
        partyType:
          type: string
          enum:
          - "[code=PERSON]"
          - "[code=COMPANY]"
          - "[code=DRIVER]"
        unstructuredName:
          type: string
        id:
          type: integer
          format: int32
        address:
          $ref: "#/components/schemas/AddressDTO"
        emailAddress:
          type: string
        phoneNumber:
          type: string
        partyRoles:
          type: array
          items:
            $ref: "#/components/schemas/PartyRoleDTO"
        consents:
          type: array
          items:
            $ref: "#/components/schemas/ConsentDTO"
    PartyRoleDTO:
      type: object
      properties:
        partyId:
          type: integer
          format: int32
        riskId:
          type: integer
          format: int32
        roleType:
          type: string
          enum:
          - "[code=PRINCIPAL_DRIVER]"
          - "[code=OCCASIONAL_DRIVER]"
          - "[code=REGISTERED_OWNER]"
          - "[code=BUSINESS_OWNER]"
    PolicyHolderDTO:
      type: object
      properties:
        partyId:
          type: integer
          format: int32
        numberOfYearsWithCurrentInsurer:
          type: integer
          format: int32
    QuoteDTO:
      type: object
      properties:
        id:
          type: string
        number:
          type: string
        pvId:
          type: string
        subBrokerNbr:
          type: string
        parties:
          type: array
          items:
            $ref: "#/components/schemas/PartyDTO"
        policyHolders:
          type: array
          items:
            $ref: "#/components/schemas/PolicyHolderDTO"
        risks:
          type: array
          items:
            $ref: "#/components/schemas/VehicleDTO"
        drivers:
          type: array
          items:
            $ref: "#/components/schemas/DriverDTO"
        policyDiscountCode:
          type: string
    VehicleDTO:
      type: object
      properties:
        id:
          type: integer
          format: int32
        modificationCode:
          type: string
          enum:
          - NEW
          - REMOVED
          - SUBSTITUTED
          - MODIFIED
        commercialUsageCategoryCd:
          type: string
        commercialUsageCd:
          type: string
        commercialUsageSpecificCd:
          type: string
        offers:
          type: array
          items:
            $ref: "#/components/schemas/OfferDTO"
        year:
          type: integer
          format: int32
        make:
          type: string
        model:
          type: string
        modelCode:
          type: string
        usageModified:
          type: boolean
        purchasedDate:
          type: string
          format: date
        businessKmPerYear:
          type: integer
          format: int32
        kmPerYear:
          type: integer
          format: int32
        trackingSystemInd:
          type: boolean
        trackingSystemCode:
          type: string
          enum:
          - BANSHEE_ICALOCK
          - ARMED_GUARD_SHERLOCK
          - VIPER
          - MERLIN
          - HAWK_200_AUTOLAND
          - OTHER
          - ADAV
          - AUTOGRAPH
          - AMERI_KOP
          - AUTOLUCK
          - AUTOMOBILE_MANUFACTURER
          - GUARDIAN
          - LARLOK
          - OTOPROTEC
          - SHERLOCK
          - VIN_LOCK
          - ULTRACAR
          - BOOMERANG_1
          - BOOMERANG_2
          - INTERCEPTER_STAR_TRACK
          - LYNX
          - MOBILIUS
          - NAVLYNX_AUTOGUARD
          - ON_STAR
          - SATELINX
          - SPAVTRACK
          - ECONOTRACK
          - TAG
          - DATADOTDNA
          - GLOBALGLOBALI
          - MICRODOTDNA
          - THREEEYETRACKING
          - CELLUTRACK
          - LOJACKBOOMERANG
          - MLINK
          - ORCA
          - VIGILGPS
          - BARRACUDA
          - KOLOMBO
        normalRadiusKm:
          type: integer
          format: int32
        grossVehicleWeight:
          type: number
          format: double
        useOfVehicleCode:
          type: string
        midHaulingDaysPerMonth:
          type: integer
          format: int32
        multiVehicleDiscountEligibilityInd:
          type: boolean
        maximumRadiusKm:
          type: integer
          format: int32
    ErrorDTO:
      type: object
      properties:
        code:
          type: string
        field:
          type: string
        message:
          type: string
        stackTrace:
          type: string
    ResponseDTO:
      type: object
      properties:
        data:
          $ref: "#/components/schemas/QuoteDTO"
        errors:
          type: array
          items:
            $ref: "#/components/schemas/ErrorDTO"
    ModelDTO:
      type: object
      properties:
        code:
          type: string
        value:
          type: string
    MakeDTO:
      type: object
      properties:
        code:
          type: string
        value:
          type: string
    DistributorDTO:
      type: object
      properties:
        logoBase64:
          type: string
        logoMimeType:
          type: string
        name:
          type: string
        number:
          type: string
        phoneNumber:
          type: string
