<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.4</version>
	</parent>

	<groupId>intact.web.autoquote-backend</groupId>
	<artifactId>autoquote-backend</artifactId>
	<version>1.28.1-SNAPSHOT</version>
	<packaging>pom</packaging>

	<modules>
		<module>autoquote-backend-web</module>
		<module>autoquote-backend-contract</module>
		<module>autoquote-backend-integration-tests</module>
	</modules>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<build_time>${timestamp}</build_time>
		<build_version>${project.version}</build_version>
		<maven.javadoc.skip>true</maven.javadoc.skip>
		<mavenToolChainsPluginVersion>3.2.0</mavenToolChainsPluginVersion>
		<mavenJacocoPluginVersion>0.8.13</mavenJacocoPluginVersion>
		<!-- mandatory properties-->
		<jdk.version>21</jdk.version>
		<jdk.vendor>temurin</jdk.vendor>
		<jdk>${jdk.version}</jdk>
		<java.version>${jdk.version}</java.version>
		<esapi.version>*******</esapi.version>
		<spring-boot.version>3.5.4</spring-boot.version>
		<springdoc-openapi-starter-webmvc-ui.version>2.8.9</springdoc-openapi-starter-webmvc-ui.version>
		<disruptor.version>4.0.0</disruptor.version>
		<jakarta.servlet-api.version>6.1.0</jakarta.servlet-api.version>
		<jakarta.validation-api.version>3.1.1</jakarta.validation-api.version>
		<glb.version>5.0.0</glb.version>
		<dependencies_ss_version>*******</dependencies_ss_version>
		<sonar.coverage.exclusions>**/*DTO.*,**/*Application.*,**/*Config.*</sonar.coverage.exclusions>
		<!-- replace with your repo info-->
		<gitHubHostUrl>https://githubifc.iad.ca.inet</gitHubHostUrl>
		<gitHubRepoName>autoquote-backend</gitHubRepoName>
		<gitHubOrg>lab-se</gitHubOrg>

		<!-- Brand Error Management-->
		<checkstyle-maven-plugin.version>3.6.0</checkstyle-maven-plugin.version>
		<digital-lab-java-lint-rules.version>1.0.2</digital-lab-java-lint-rules.version>
		<sonar.java.checkstyle.reportPaths>target/checkstyle-result.xml</sonar.java.checkstyle.reportPaths>

		<!-- Important for pitest cache, CI will override these values-->
		<pitest.historyFile>target/pit-history.txt</pitest.historyFile>
	</properties>

	<scm>
		<url>${gitHubHostUrl}/${gitHubOrg}/${gitHubRepoName}</url>
		<connection>scm:git:${gitHubHostUrl}/${gitHubOrg}/${gitHubRepoName}.git</connection>
		<developerConnection>scm:git:${gitHubHostUrl}/${gitHubOrg}/${gitHubRepoName}.git
		</developerConnection>
		<tag>HEAD</tag>
	</scm>

	<distributionManagement>
		<repository>
			<id>intact-releases</id>
			<name>intact releases repository</name>
			<url>https://prod-nexus-b2eapp.iad.ca.inet:8443/nexus/content/repositories/releases/</url>
		</repository>
		<snapshotRepository>
			<id>intact-snapshots</id>
			<name>intact snapshots repository</name>
			<url>https://prod-nexus-b2eapp.iad.ca.inet:8443/nexus/content/repositories/snapshots/</url>
		</snapshotRepository>
	</distributionManagement>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>4.5.0</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>4.5.14</version>
			</dependency>

			<dependency>
				<groupId>org.springdoc</groupId>
				<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
				<version>${springdoc-openapi-starter-webmvc-ui.version}</version>
			</dependency>

			<dependency>
				<groupId>com.lmax</groupId>
				<artifactId>disruptor</artifactId>
				<version>${disruptor.version}</version>
			</dependency>

			<dependency>
				<groupId>jakarta.servlet</groupId>
				<artifactId>jakarta.servlet-api</artifactId>
				<version>${jakarta.servlet-api.version}</version>
				<scope>provided</scope>
			</dependency>

			<dependency>
				<groupId>jakarta.validation</groupId>
				<artifactId>jakarta.validation-api</artifactId>
				<version>${jakarta.validation-api.version}</version>
				<scope>provided</scope>
			</dependency>

			<dependency>
				<groupId>co.elastic.logging</groupId>
				<artifactId>log4j2-ecs-layout</artifactId>
				<version>${ecs-logging-java.version}</version>
			</dependency>

			<dependency>
				<groupId>intact.commons.service.plp</groupId>
				<artifactId>plp-api</artifactId>
				<version>${plp.version}</version>
			</dependency>

			<dependency>
				<groupId>intact.commons.service.plp</groupId>
				<artifactId>plp-services</artifactId>
				<version>${plp.version}</version>
			</dependency>

			<!-- Fix for CVE-2025-48734 -->
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>1.11.0</version>
			</dependency>

			<dependency>
				<groupId>commons-collections</groupId>
				<artifactId>commons-collections</artifactId>
				<version>3.2.2</version>
			</dependency>

			<dependency>
				<groupId>org.owasp.esapi</groupId>
				<artifactId>esapi</artifactId>
				<version>${esapi.version}</version>
			</dependency>

			<dependency>
				<groupId>intact.commons.service.glb</groupId>
				<artifactId>persist-glb-api</artifactId>
				<version>${glb.version}</version>
			</dependency>
			<dependency>
				<groupId>intact.commons.service.glb</groupId>
				<artifactId>persist-glb-services</artifactId>
				<version>${glb.version}</version>
			</dependency>

			<dependency>
				<groupId>intact.ss</groupId>
				<artifactId>ssimpl</artifactId>
				<version>${dependencies_ss_version}</version>
				<exclusions>
					<exclusion>
						<artifactId>som</artifactId>
						<groupId>intact.som</groupId>
					</exclusion>
					<exclusion>
						<groupId>intact.commons</groupId>
						<artifactId>dependencies-ss-plp</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>intact.ss</groupId>
				<artifactId>ssdelegate</artifactId>
				<version>${dependencies_ss_version}</version>
				<exclusions>
					<exclusion>
						<artifactId>som</artifactId>
						<groupId>intact.som</groupId>
					</exclusion>
					<exclusion>
						<groupId>intact.som</groupId>
						<artifactId>somh</artifactId>
					</exclusion>
					<exclusion>
						<groupId>intact.som</groupId>
						<artifactId>somcl</artifactId>
					</exclusion>
					<exclusion>
						<groupId>intact.commons</groupId>
						<artifactId>dependencies-ss-plp</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<!-- Fix for CVE-2025-48976 -->
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>1.6.0</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<profiles>
		<profile>
			<!-- Used to activate toolchains plugin if the toolchains.xml
                config file is available -->
			<id>MVN_TOOLCHAINS</id>
			<activation>
				<file>
					<exists>${user.home}/.m2/toolchains.xml</exists>
				</file>
			</activation>

			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-toolchains-plugin</artifactId>
						<version>${mavenToolChainsPluginVersion}</version>
						<executions>
							<execution>
								<phase>validate</phase>
								<goals>
									<goal>toolchain</goal>
								</goals>
							</execution>
						</executions>
						<configuration>
							<toolchains>
								<jdk>
									<version>${jdk.version}</version>
									<vendor>${jdk.vendor}</vendor>
								</jdk>
							</toolchains>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-checkstyle-plugin</artifactId>
						<version>${checkstyle-maven-plugin.version}</version>
						<configuration>
							<configLocation>checkstyle.xml</configLocation>
							<failOnViolation>false</failOnViolation>
						</configuration>
						<executions>
							<execution>
								<goals>
									<goal>check</goal>
								</goals>
							</execution>
						</executions>
						<dependencies>
							<dependency>
								<groupId>intact.lab</groupId>
								<artifactId>digital-lab-java-lint-rules</artifactId>
								<version>${digital-lab-java-lint-rules.version}</version>
							</dependency>
						</dependencies>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>env-pitest-history</id>
			<activation>
				<property>
					<name>env.PITEST_HISTORY_FILE</name>
				</property>
			</activation>
			<properties>
				<pitest.historyFile>${env.PITEST_HISTORY_FILE}</pitest.historyFile>
			</properties>
		</profile>
	</profiles>
</project>
