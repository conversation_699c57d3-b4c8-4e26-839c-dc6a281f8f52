#----------------------------------------------------------------------
# file:   webattack.properties
# author: <PERSON>
#----------------------------------------------------------------------
# This file controls the web attack verification. By default each rate call is verified
#
# CAUTION : 
# This file is dynamically reloaded. Any misconfiguration might lead to 
# SECURITY BREACH. In doubt, set bypasswebattackverification.mode=off.
#----------------------------------------------------------------------  

#----------------------------------------------------------------
# !!!!!!!!!!!!!!!!!!!!!!!! CAUTION !!!!!!!!!!!!!!!!!!!!!!!!!!!!!! 
#If set to "on" or "auto" the web attack protection will be disabled.

# Possible values: on | off | auto
bypasswebattackverification.mode=on

# List of all scheduled bypass web attack verification
# webattack.mode must be set to "auto"
# Patterns : schedule.yyyy.mm.dd=startTime/durationInMinutes
# Examples : "schedule.2009.12.31=23:30/60"
schedule.2015.11.16=12:50/3

#----------------------------------------------------------------
# General configuration 
#----------------------------------------------------------------
# Normal config (this file) refresh rate in seconds : 60s=1min*60seconds
seconds.between.config.file.reloads=60
# Config refresh rate when bypasswebattackverification mode is on (likely more frequent)
seconds.between.config.file.reloads.bypasswebattackverification.on=60

mail.smtp.host=smtp-gtw-np.iad.ca.inet
mail.from.address=<EMAIL>
# The list of recipients that will receive notification when 'bypass web attack verification' flag get changed. 
# Obviously, no email will be sent when no recipients. Mails should be separated by , char.    
mail.recipient.list = <EMAIL>
mail.subject.bypass = WebAttack verification (Intact)
mail.text.bypass.on = Bypassing WebAttack verification (Intact) is on!
mail.text.bypass.off = Bypassing WebAttack verification (Intact) is off!

webattack_qquote.level_1.IpRestrictionDurationMinutes=180
webattack_qquote.level_2.IpRestrictionDurationMinutes=43200

webattack_qquote.level_1.RATINGREQ.IpRestrictionType=RDBK1
webattack_qquote.level_2.RATINGREQ.IpRestrictionType=RDBK2

webattack_qquote.level_1.CLICLMHIST.IpRestrictionType=RDBK1
webattack_qquote.level_2.CLICLMHIST.IpRestrictionType=RDBK2