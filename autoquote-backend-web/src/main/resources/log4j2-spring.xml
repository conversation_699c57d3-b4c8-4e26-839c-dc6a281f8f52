<?xml version="1.0" encoding="UTF-8"?>

<configuration>
    <Appenders>
        <SpringProfile name="local">
<!--            <Console name="LogbookAppender">-->
<!--                <PatternLayout pattern="%d [%X] [%t] [%p] %c{1.} - %m %n"/>-->
<!--            </Console>-->

            <Console name="ConsoleAppender" target="SYSTEM_OUT" follow="true">
                <PatternLayout pattern="%d [%X] [%t] [%p] %c{1.} - %m %n"/>
            </Console>
        </SpringProfile>

        <SpringProfile name="!local">
<!--            <NoSql name="LogbookAppender">-->
<!--                <MongoDb4 connection="mongodb://${spring:mongo.logbook.userName}:${spring:mongo.logbook.password}@${spring:mongo.logbook.hosts}/${spring:mongo.logbook.databaseName}?authSource=${spring:mongo.logbook.authenticationDatabase};replicaSet=${spring:mongo.logbook.replicaset};readPreference=primary;ssl=true" />-->
<!--                <MessageLayout />-->
<!--            </NoSql>-->

            <Console name="ConsoleAppender" target="SYSTEM_OUT" follow="true">
                <EcsLayout serviceName="autoquote-backend"/>
            </Console>
        </SpringProfile>
    </Appenders>

    <Loggers>
        <!-- Mongo logger -->
<!--        <Logger name="com.intact.logbook.core.Logbook" level="${spring:logging.logbook.level:-TRACE}" additivity="false">-->
<!--            <Appender-ref ref="LogbookAppender"/>-->
<!--        </Logger>-->

        <Logger name="org.springframework" level="INFO"/>

        <Root>
            <AppenderRef ref="ConsoleAppender"/>
        </Root>
    </Loggers>
</configuration>
