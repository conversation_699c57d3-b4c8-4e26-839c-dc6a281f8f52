#These properties are dynamically reloaded every 2 minutes

#Chat Button Properties
#Commented or empty properties indicates that the chat button will not be displayed.
#In chatImageServer replace : with %3A
chatImageServer=
#Talisma questionnaire id
chatQuestID_en_ON=
chatQuestID_fr_ON=
chatQuestID_en_QC=
chatQuestID_fr_QC=
Talisma portal style id
chatPortID_en_ON=
chatPortID_fr_ON=
chatPortID_en_QC=
chatPortID_fr_QC=

# WebTracking
config.tracking.mobile=false
config.tracking.standard=true
config.web_tracking_google_account_id=UA-********-13
config.web_tracking_omniture_account_id=ingcainsurancedev

# To activate promo rebate banner (100$ welcome gift)
activeRebateAdQC=true

#UBI Activation
config.ubi.QC=true
config.ubi.ON=true
config.ubi.AB=true

config.ubi2.QC=true
config.ubi2.ON=true
config.ubi2.AB=true

#Advisor is activated or not
config.advisor.activated=true

#Web Improvement Phase 2 - Installation date
config.web.improvement.date=2014-11-09

# This to select which implementation to match client, Previously, new algorithm was used only by QuickQuote 
client.matching.algorithme.use.legacy=false

##---------------------------------------------------------------------------------------
## DriverBusinessProcess - contextual relationships
driver.relationship.code.spouse=C
driver.relationship.text.spouse.fr.m=Mon conjoint
driver.relationship.text.spouse.fr.f=Ma conjointe
driver.relationship.text.spouse.en=My spouse
driver.relationship.code.child=E
driver.relationship.text.child.fr=Mon enfant
driver.relationship.text.child.en=My child
driver.relationship.code.father=M
driver.relationship.text.father.fr=Mon p\u00E8re
driver.relationship.text.father.en=My father
driver.relationship.code.mother=M
driver.relationship.text.mother.fr=Ma m\u00E8re
driver.relationship.text.mother.en=My mother
driver.relationship.code.brother=F
driver.relationship.text.brother.fr=Mon fr\u00E8re
driver.relationship.text.brother.en=My brother
driver.relationship.code.sister=F
driver.relationship.text.sister.fr=Ma soeur
driver.relationship.text.sister.en=My sister
driver.relationship.code.other1=X1
driver.relationship.text.other1.fr=Autre - vivant sous mon toit
driver.relationship.text.other1.en=Other - living with me
driver.relationship.code.other2=X2
driver.relationship.text.other2.fr=Autre - ne vivant pas sous mon toit
driver.relationship.text.other2.en=Other - not living with me