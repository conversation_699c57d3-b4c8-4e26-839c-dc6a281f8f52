#default in all environment
management.endpoints.web.exposure.include: ${MANAGEMENT_ENDPOINTS:info,health,prometheus}
#property exposed at runtime as a configmap, with default value

spring:
  main:
    allow-circular-references: true
  jpa:
    properties:
      hibernate:
        transaction:
          jta:
            platform: Atomikos
        current_session_context_class: org.springframework.orm.hibernate5.SpringSessionContext
        connection.handling_mode: DELAYED_ACQUISITION_AND_RELEASE_AFTER_TRANSACTION
        transaction.coordinator_class: jta
        jta.track_by_thread: false
        enable_lazy_load_no_trans: false
        transaction.auto_close_session: false
        transaction.flush_before_completion: false
      id.sequence.increment_size_mismatch_strategy: FIX

application:
  id:
    ontario: AQQK
    others: BWAQ

cors:
  allowed:
    origins: ${cors.allowed.origins}

atomikos:
  properties:
    log-base-name: autoquote_backend_log
    log-base-dir: /atomikos/log/

# Logging
logging:
  level:
    root: INFO
    org.springframework: INFO
    intact.lab.template: INFO
  logbook:
    enabled: true
    level: INFO

blacklistedModels: 0271,1031,1949,1997,7658,7659,7660,7670,7739,7740,7004,7005,7083,7181,7182,7184,7200,7242,0996,1511,1517,1565,2753,2756,2799,2855,2884,2888,1400,1584,1960,3294,3295,3544,3546,3558,3559,3560,3561,3562,3563,3574,3577,3629,3630,3635,3645

recaptcha:
  api:
    url: https://quoters-intg-recaptcha-api.apps.np.iad.ca.inet
  enabled: false
