#allow override of values for local environment
#add -Dspring.profiles.active=local in your vm options
management.endpoints.web.exposure.include: '*'

# Logging
logging:
  level:
    root: INFO
    org.springframework: INFO
    intact.lab.template: INFO
  logbook:
    enabled: true
    level: INFO

cors:
  allowed:
    origins: '*'

database:
  plp:
    user: Plpqqint
    password: #See Vault
    url: **********************************************************************************************************************************)))
    jdbc:
      jndiName: jdbc/plpolicy
      initialSize: 1
      maxTotal: 5
      maxIdle: 2
  cif:
    jdbc:
      jndi-name: jdbc/CIF
      initialSize: 1
      maxTotal: 12
      maxIdle: 1
    user: Cifqqint
    password: #See Vault
    url: **********************************************************************************************************************************)))

bloom-mq-handler-service:
  url: https://quoters-uat-bloom-mq-handler.apps.np.iad.ca.inet/mqhandler/v1/message
  isEnabled: true

atomikos:
  properties:
    log-base-name: atomikos-persistence
    log-base-dir: ./logs

recaptcha:
  api:
    url: https://quoters-intg-recaptcha-api.apps.np.iad.ca.inet
  enabled: false
