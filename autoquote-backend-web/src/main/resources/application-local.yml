#allow override of values for local environment
#add -Dspring.profiles.active=local in your vm options
management.endpoints.web.exposure.include: '*'

# Logging
logging:
  level:
    root: INFO
    org.springframework: INFO
    intact.lab.template: INFO
  logbook:
    enabled: true
    level: INFO

cors:
  allowed:
    origins: '*'

database:
  plp:
    user: Plpqqint
    password: Z6kYUoMOw)8
    url: **********************************************************************************************************************************)))
    jdbc:
      jndiName: jdbc/plpolicy
      initialSize: 1
      maxTotal: 5
      maxIdle: 2
  cif:
    jdbc:
      jndi-name: jdbc/CIF
      initialSize: 1
      maxTotal: 12
      maxIdle: 1
    user: Cifqqint
    password: B:FAA0:WsA67
    url: **********************************************************************************************************************************)))

bloom-mq-handler-service:
  url: https://quoters-uat-bloom-mq-handler.apps.np.iad.ca.inet/mqhandler/v1/message
  isEnabled: true

atomikos:
  properties:
    log-base-name: atomikos-persistence
    log-base-dir: ./logs

recaptcha:
  api:
    url: https://quoters-intg-recaptcha-api.apps.np.iad.ca.inet
  enabled: false

document:
  root: https://uat-intact-sso.iad:ca.inet/on/secure/files/apps

dynamic_banner_url:
  https://uat-intact-sso.iad.ca.inet/on/secure/files/apps/dynamic-banner

dynamic_feedback_url:
  https://uat-intact-sso.iad.ca.inet/on/secure/files/apps/dynamic-feedback

broker:
  location:
    url:
      en: https://intact.koremtest.com/?form=locator_search&CHANGEBROKER=Y&lang=EN
      fr: https://intact.koremtest.com/?form=locator_search&CHANGEBROKER=Y&lang=FR

webmethods:
  endpoint:
    prefix: "https://uat-webmethods.iad.ca.inet:9420/ws/SNB.descriptors:"

helptext:
  document:
    root:
      url: https://uat-intact-sso.iad.ca.inet/on/secure/files/apps/helptext/

home:
  page:
    url: https://uat-intact-sso.iad.ca.inet/wep/WEP/homePage.do

serviceLocator:
  hostName: uat-pega.iad.ca.inet

mail:
  smtp:
    host: uat-smtp.iad.ca.inet
    username: ${MAIL_SMTP_USERNAME}
    password: ${MAIL_SMTP_PASSWORD}
    auth: false
    port: 25
    starttls:
      enable: false
    ssl:
      enable: false
  debug: true
  transport:
    protocol: smtp

public:
  images:
    uri: https://uat-intact-sso.iad.ca.inet/files/apps/email/images
  autoquote:
    images:
      uri: https://uat-intact-sso.iad.ca.inet/on/AutoQuote/image
  webzone:
    access:
      uri: https://uat-brokers.iad.ca.inet/webzone/WebZone/index.jsf?referenceNo=

notif:
  incomplete:
    quote:
      override: <EMAIL>
  env: .uat

base:
  url: https://uat-intact-sso.iad.ca.inet/wep/WEP/

config:
  web:
    tracking:
      google:
        account:
          id: UA-********-13
      omniture:
        account:
          id: ingcainsurancedev
  ubi2:
    QC: true
    ON: true
    AB: true
  advisor:
    activated: true

date:
  overriding:
    rating:
      QC: ********
      AB: ********
      ON: ********
  enable:
    BR16746:
      ON: 2019-10-15

default:
  subBroker:
    number: 0060

application:
  image:
    url:
      env: uat
      belairdirect: -intact
      domain: -sso.iad.ca.inet
      junction: ""

environnement-level: TE1

CIF_CLIENT_CREATE_STORED_PROCEDURE_WITH_PARAMS: CIFADMIN.cif_class_person.add_object_person(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
CREATE_CLIENT_WITH_DIRECT_CHAN_DIST: true

mqa4:
  bloom:
    password: ${MQA4_BLOOM_PASSWORD}
  queue:
    manager:
      name: MQA4
  hostname: MQA4
  port: 1415
  channel: CLIENT.TO.MQA4
  userID: bloom

mongo:
  logbook:
    hosts: stha2n14783.iad.ca.inet:27021
    replicaset: logbook01UAT
    databaseName: logbookdb
    authenticationDatabase: admin
    userName: logbook-appender-user
    password: ${LOGBOOK_MONGO_PASSWORD}

bypasswebattackverification:
  mode: on
