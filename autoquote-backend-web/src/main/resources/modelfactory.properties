# modelfactory.properties
# 
# This property file is loaded by
# com.ing.canada.sombase.ModelFactory (sombase.jar)
# 
# "model_count" is the number of modelX variable you need.
# "modelX" where X is a number between 1 and "model_count".
#    The value of "modelX" is the package name for the model.
#    Example: com.ing.canada.som.interfaces....
#       --> modelX = som
#    Example: com.ing.canada.somh.interfaces....
#       --> modelX = somh
# 
# The order is not important.
# 
# For example (one model):
# 	model_count = 1
#   model1 = som
# 
# For example (two models):
# 	model_count = 2
#   model1 = som
#   model2 = somh
#

model_count = 1

model1 = som