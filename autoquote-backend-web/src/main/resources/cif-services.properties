CIF_SCHEMA=CIFADMIN
CIF_PARTY_UTILITY_PACKAGE=CIFADMIN.party_utility.
CIF_RANDOM_UNIQUE_ID_PACKAGE=CIFADMIN.random_unique_id.
EMPLOYE_PACKAGE=EMPADMIN.emp_class_employes.
# /// Client search store proc ///
CIF_CLIENT_SEARCH_STORED_PROCEDURE=CIFADMIN.cif_class_search_soa.client_search
# /// Client creation store proc /// 
CIF_CLIENT_CREATE_STORED_PROCEDURE_WITH_PARAMS=CIFADMIN.cif_class_person.add_object_person(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
CREATE_CLIENT_WITH_DIRECT_CHAN_DIST=true

SHOW_SQL=false
BATCH_SIZE=250
RMI_REGISTRY_PORT=1199
#cif-dataSource.jndi-name=jdbc/CIF
#JTA_PLATFORM=org.hibernate.engine.transaction.jta.platform.internal.WebSphereExtendedJtaPlatform

cif-dataSource.jndi-name=java:comp/env/jdbc/CIF
#JTA_PLATFORM=org.hibernate.engine.transaction.jta.platform.internal.BitronixJtaPlatform
JTA_PLATFORM=org.hibernate.engine.transaction.jta.platform.internal.AtomikosJtaPlatform
#org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform
