# hibernate.hbm2ddl.auto values :
# validate: validate the schema, makes no changes to the database.
# update: update the schema.
# create: creates the schema, destroying previous data.
# create-drop: drop the schema at the end of the session.
HBM2DDL=

SHOW_SQL=false

#Environment specific configuration
# In fact not used in Junits, only in WebSphere
#global-dataSource.jndi-name=jdbc/plpolicy
global-dataSource.jndi-name=java:comp/env/jdbc/plpolicy
#JTA_PLATFORM=org.hibernate.engine.transaction.jta.platform.internal.WebSphereExtendedJtaPlatform
JTA_PLATFORM=org.hibernate.engine.transaction.jta.platform.internal.BitronixJtaPlatform