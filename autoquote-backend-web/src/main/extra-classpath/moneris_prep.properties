#----------------------------------General <PERSON><PERSON><PERSON> configuration -----------------------------

#Moneris Crypt type
external_payment_crypt_type=2

#------------------------------General <PERSON><PERSON><PERSON> java api configuration ------------------------

#Mon<PERSON>s java api host external configuration
external_payment_host=esqa.moneris.com


#---------------------------------General <PERSON><PERSON><PERSON> HVARU configuration-------------------------

#Mon<PERSON>s HVARU URL external configuration
external_payment_url=https://esqa.moneris.com/HPPDP/index.php


#---------------------------------General <PERSON><PERSON><PERSON> Hosted Card Tokenization Profile------------

#Moneris Hosted Card Tokenization Profile URL external configuration
external_hosted_card_tokenization_profile_url=https://esqa.moneris.com/HPPtoken/index.php

#Response url for hvaru (optional) to be used by redirect.jsp
#set the response url in the hvaru configuration to redirect.jsp
#agent web payment application
response_url_A_WCCT=https://prep-signature-sso.iad.ca.inet/webpayment-intact/WebPaymentApplication/response.do
#broker web payment application
response_url_B_WCCT=https://prep-brokers.iad.ca.inet/webpayment-intact/WebPaymentApplication/response.do
#agent Autoquote application
response_url_A_BWAQ=
#client Autoquote application
response_url_C_BWAQ=

#---------------------------------Moneris store configurations-------------------------

#Moneris list of store
external_payment_nbStore=1
external_payment_store_1=A_QC




#--------------------------------------------------------------------------
#
#      INTACT QUEBEC config
# 
#--------------------------general configuration---------------------------
external_payment_store_name_A_QC_fr=Intact Quï¿½bec
external_payment_store_name_A_QC_en=Intact Quï¿½bec
#-----------------------Moneris java api configuration --------------------
#Plan B
external_payment_store_B_A_QC=
external_payment_api_token_B_A_QC=
#Plan E
external_payment_store_E_A_QC=monca00158
external_payment_api_token_E_A_QC=43NIEy0r75YiG3hJaanP
#
#--------------------------WCCT HVARU configurations ----------------------
#Agent Moneris config
#Plan B
external_payment_res_id_B_WCCT_A_A_QC=
external_payment_res_key_B_WCCT_A_A_QC=
#Plan E
external_payment_res_id_E_WCCT_A_A_QC=CMFQV00158
external_payment_res_key_E_WCCT_A_A_QC=resI19U9IYA3
#Broker Moneris config
#Plan B
external_payment_res_id_B_WCCT_B_A_QC=
external_payment_res_key_B_WCCT_B_A_QC=
#Plan E
external_payment_res_id_E_WCCT_B_A_QC=24MHP00158
external_payment_res_key_E_WCCT_B_A_QC=resBB4PBZJA4
#------------------------AutoQuote Hosted Card Tokenization Profile -------
#Agent Moneris config
#Plan B
external_hosted_card_tokenization_profile_B_BWAQ_A_A_QC=
#Plan E
external_hosted_card_tokenization_profile_E_BWAQ_A_A_QC=
#Customer Moneris config
#Plan B
external_hosted_card_tokenization_profile_B_BWAQ_C_A_QC=
#Plan E
external_hosted_card_tokenization_profile_E_BWAQ_C_A_QC=ht4H1IAO2LRZK51
#
#--------------------------------------------------------------------------
