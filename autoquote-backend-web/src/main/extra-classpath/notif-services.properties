#
# Autoquote Notifications settings.
#

mail.smtp.host=uat-smtp.iad.ca.inet
mail.smtp.username=autoquote-intact-np
mail.smtp.password=PL4HYZvp
mail.smtp.auth=true
mail.smtp.port=25
mail.transport.protocol=smtp
mail.smtp.starttls.enable=true
mail.smtp.ssl.enable=false
mail.debug=true

mail.cc.address=<EMAIL>
#Regex pattern that a email address must match to send a notification. Must be uppercase. Keep empty to match all
mail.pattern=

public.images.uri=https://uat-intact-sso.iad.ca.inet/files/apps/email/images
public.webzone.access.uri=https://uat-brokers.iad.ca.inet/webzone/WebZone/index.jsf?referenceNo=

# for retrieve quote
base.url.*.*.*=https://uat-intact-sso.iad.ca.inet/wep/WEP/

# Debug data
debug.mode=false
force.email=

# Distribution Channel (B=Broker, D=Direct)
distribution.channel=B


# xpham RANB-12 : email new images are temporary added in AutoQuote, waiting for static website managed by MMCM to be under our control   
public.autoquote.images.uri=https://uat-intact-sso.iad.ca.inet/on/AutoQuote/image
mail.from.address.*.*.*=<EMAIL>
mail.from.address.QC.*.*=<EMAIL>
mail.from.address.ON.*.*=<EMAIL>
mail.from.address.AB.*.*=<EMAIL>
mail.from.address.BC.*.*=<EMAIL>
mail.cc.address.QC=<EMAIL>
mail.cc.address.ON=<EMAIL>
mail.cc.address.AB=<EMAIL>
mail.cc.address.BC=
mail.to.intact.address.BC=

#RANB-172 for incomplete quote email, if not blank, will override the send to so 
#brokers don't receive notifications.
incomplete.quote.override.broker.address=<EMAIL>