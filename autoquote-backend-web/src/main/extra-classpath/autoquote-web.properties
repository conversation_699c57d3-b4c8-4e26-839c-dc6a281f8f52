#
# AutoquoteWeb external configuration file
#

# This value is used when creating bean for a specific company 
#	Ex:	@StrutsActionComponent(value = "/driverQCINTACT", province = QUEBEC)	
# Also by the jspvars.jsp to set the resource package name

company= Intact

# This value is used by the match client. It will search for client
# associated to this csioNbr.
# Usage : Can be use with one value
#			csioNbr=BEL
#		: Or with multiple values
#			csioNbr=GC,WU
csioNbr=GC

#Cluster name to be displayed in technical error page 
#NOTE: For security issue, do not use the physical machine name 
load.balancing.cluster.name=c1

# Maintenance page roadblock
# IMPORTANT: Make sure this page contains the string "MAINTENANCE_ROADBLOCK" somewhere in its 
#            source (comments) so the Ajax calls recognize it. 
maintenance.page.roadblock=https://uat-intact-sso.iad.ca.inet/files/apps/maintenance/maintenance.htm

# [Technical error page]
# In DEV & TEST env. set this to true -> the stacktrace will be always be displayed (developers, testers, agents & customers)
# In PROD env. set this to false -> only agents will see the stack trace
technical.error.stacktrace.always=true


# paths
application-image-url=
application-email-from-name=intact
application-email-from-address=<EMAIL>

# portfolio urls
portfolio_secured_login_url=
portfolio_lost_password_url=
portfolio_lost_userid_url=
portfolio_url=
portfolio_registration_url=

# agent home page
home_page_agent_url=AutoQuote/agent.do

# belair home page (client)
home_page_client_url=index{0}{1}.htm

# Url to kanetix
kanetix_url = http://demo.kanetix.ca/BA_compare

# FSCO - Ontario Auto Reform 
new_accident_benefits_available_date=2010-05-01
# FSCO - Ontario Auto Remediation
ontario_remediation_available_date=2010-05-01

# Where 2 Get it (W2GI) base url
brokerLocatorUrl.en = https://intact.koremtest.com/?form=locator_search&CHANGEBROKER=Y&lang=EN
brokerLocatorUrl.fr = https://intact.koremtest.com/?form=locator_search&CHANGEBROKER=Y&lang=FR

# introduction video about intact broker
introductionVideo.url.fr = http://intact-video.mmcm.com/nouveausite2011/videos/Intact_Fr_07122010.flv
introductionVideo.url.en = http://intact-video.mmcm.com/nouveausite2011/videos/Intact_En_07122010.flv
introductionVideo.flowplayer.url = http://media.intactinsurance.com/files/bumpbox_en/flowplayer.commercial-3.1.5.swf
introductionVideo.flowplayer.key=#@321ae86fadfa6cc7750

# Default sub-broker, for this company-province instance,
# when cannot find one for some postal codes.
default.subBroker.number=0060

# [HelpTextUrl tag] 
# See com.ing.canada.common.web.tags.HelpTextUrl.HelpTextUrl for more detail 
helptext.documentRoot=/PortfolioDocument/help/help/help/CustomerDriven/
helptext.extension=.html

# Deployement domain (without the subdomain)
deployment.domain=
#
# BazaarVoice peer reviews integration
# ----------------------------------------- 
review.url=
# https://reviews.ca.ecamericas/bvstaging/1024-fr_ca/2000002/submission.htm?format=embedded&campaignid=BV_RATING_SUMMARY&return=https%3A%2F%2Fdev-belairdirect-sso.iad.ca.inet%2Flstoica%2FPortfolio%2Findex.do&innerreturn=https%3A%2F%2Freviews.ca.ecamericas%2Fbvstaging%2F1024-fr_ca%2F2000002%2Freviews.htm%3Fformat%3Dembedded&user=__USERID__&submissionparams=lo%3DTODO_lstoica_REPLACE%3Bpl%3DTODO_lstoica_REPLACE&submissionurl=https%3A%2F%2Fdev-belairdirect-sso.iad.ca.inet%2Flstoica%2FPortfolio%2Freview.do 
review.post.url=
# in production the /bvstaging should be removed + the review machine must be in the domain specified by review.domain 
review.base.url=
# it represents bvClientId + locale - the right bvClientId is replaced at runtime 
review.displayCode=
# review country can be qc or on 
review.bvClientId.on=
review.bvClientId.qc=
review.product.id=
review.readReviewLink=
review.title.fr=
review.title.en=
review.subtitle.fr=
review.subtitle.en=

#define the different value for the manufacturing context that will be use for intact.
#this include the manufacturer company, the insurance business and the distribution channel
#add the province if needed.
manufacturing.company.qc=A
manufacturing.company.on=6
manufacturing.company.ab=3
distribution.channel=B
insurance.business=R

hide_change_broker_date_from=2012-01-01
hide_change_broker_date_to=2012-01-01

# uniform
# 1008 - define entrust seal script
# ----------------------------------------------------------
entrust.url = http://www.entrust.net
# Belair seal
entrust.src.en = https://seal.entrust.net/seal.js?domain=www.intactinsurance.com&img=11
entrust.src.fr = https://seal.entrust.net/seal.js?domain=www.intactassurance.com&img=22

# In DEV & TEST env. set this to false -> banner will NOT be shown in the maintenance control page
# In PROD env. set this to true -> a banner will be shown in the maintenance control page 
production.environment=false


#Generic Broker Image
document.root=https://uat-intact-sso.iad.ca.inet/on/secure/files/apps

#dynamic banner url
#this needs to be the path to get the banner_language_province.html file that 
#we dynamically insert into the quote
dynamic_banner_url=https://uat-intact-sso.iad.ca.inet/on/secure/files/apps/dynamic_banner/
dynamic_feedback_url=https://uat-intact-sso.iad.ca.inet/on/secure/files/apps/dynamic_feedback/

### enable business rules by date
date.enable.BR16746.ON=2019-10-15
vehicle.price.limit.BR16746.ON=200000
vehicle.BR16878.blacklistedModels=0271,1031,1949,1997,7658,7659,7660,7670,7739,7740,7004,7005,7083,7181,7182,7184,7200,7242,0996,1511,1517,1565,2753,2756,2799,2855,2884,2888,1400,1584,1960,3294,3295,3544,3546,3558,3559,3560,3561,3562,3563,3574,3577,3629,3630,3635,3645
vehicle.BR16878.yearlimit=2017

# ubi 4.0
driver.ubi.intact.qc.programVersionCode=ADD_VALUE
