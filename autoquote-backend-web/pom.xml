<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>intact.web.autoquote-backend</groupId>
        <artifactId>autoquote-backend</artifactId>
        <version>1.30.1-SNAPSHOT</version>
    </parent>

    <artifactId>autoquote-backend-web</artifactId>
    <packaging>jar</packaging>

    <properties>
        <cucumber-qaa-libs.version>6.1.38</cucumber-qaa-libs.version>
        <org-pitest.version>1.19.6</org-pitest.version>
        <org-pitest-elements.version>0.6.3</org-pitest-elements.version>
        <org-pitest-junit.version>1.2.3</org-pitest-junit.version>
        <logbook.version>3.1.1</logbook.version>
        <skipITs>false</skipITs>
        <plp.version>5.1.0</plp.version>
        <cif.version>4.0.10</cif.version>
        <datamediator-com-pl.version>2.7.6</datamediator-com-pl.version>
        <moneris.version>3.1.3</moneris.version>
        <abr.version>*******</abr.version>
        <rating-simplification.version>2.2.1</rating-simplification.version>
        <jackson-datatype.version>2.19.2</jackson-datatype.version>
        <security-tools.version>3.0.2</security-tools.version>
        <autoquote-intact-resources.version>2.69.28</autoquote-intact-resources.version>
        <common-api.version>5.0.0</common-api.version>
        <access-manager.version>*******</access-manager.version>
        <sonar.coverage.exclusions>**/*Exception.java</sonar.coverage.exclusions>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <artifactId>spring-jdbc</artifactId>
            <groupId>org.springframework</groupId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-beans</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-dbcp</artifactId>
            <version>11.0.9</version>
        </dependency>

        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>23.7.0.25.01</version>
        </dependency>

        <dependency>
            <groupId>com.atomikos</groupId>
            <artifactId>transactions-spring-boot3-starter</artifactId>
            <version>6.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.18.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>intact.lab.security.recaptcha</groupId>
            <artifactId>recaptcha-api-client-annotated</artifactId>
            <version>2.16.0</version>
        </dependency>

        <!-- Micrometer core dependecy  -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
        </dependency>
        <!-- Micrometer Prometheus registry  -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>intact.test</groupId>
            <artifactId>cucumber-base</artifactId>
            <version>${cucumber-qaa-libs.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>intact.test</groupId>
            <artifactId>cucumber-component-spring</artifactId>
            <version>${cucumber-qaa-libs.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.test</groupId>
            <artifactId>cucumber-plugins</artifactId>
            <version>${cucumber-qaa-libs.version}</version>
            <exclusions>
                <exclusion>
                    <!--					excluded due to conflict with spring -->
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy-xml</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.intact.logbook</groupId>
            <artifactId>logbook-core</artifactId>
            <version>${logbook.version}</version>
        </dependency>

        <dependency>
            <groupId>intact.commons.service.plp</groupId>
            <artifactId>plp-api</artifactId>
            <version>${plp.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-core</artifactId>
                    <groupId>org.hibernate.orm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-orm</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.commons.service.plp</groupId>
            <artifactId>plp-services</artifactId>
            <version>${plp.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-core</artifactId>
                    <groupId>org.hibernate.orm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-orm</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.commons.capi</groupId>
            <artifactId>common-api-il-services</artifactId>
            <version>${common-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ojdbc6</artifactId>
                    <groupId>com.oracle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-mapper-asl</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson-datatype.version}</version>
        </dependency>

        <dependency>
            <groupId>xerces</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>2.12.2</version>
        </dependency>

        <dependency>
            <groupId>com.ibm.websphere</groupId>
            <artifactId>ibmorb</artifactId>
            <version>8.0.0</version>
        </dependency>

        <dependency>
            <groupId>intact.commons</groupId>
            <artifactId>dependencies-websphere-mq</artifactId>
            <version>7.0.1.5.0</version>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>intact.commons.com</groupId>
            <artifactId>com</artifactId>
            <version>1.63.0.10</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.commons.service.cif</groupId>
            <artifactId>cif-service-api</artifactId>
            <version>${cif.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.commons.service.cif</groupId>
            <artifactId>cif-services</artifactId>
            <version>${cif.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.commons.capi</groupId>
            <artifactId>common-api-pega-services</artifactId>
            <version>${common-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.eclipse.emf</groupId>
            <artifactId>commonj.sdo</artifactId>
            <version>2.3.0.v200802051830</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.emf</groupId>
            <artifactId>org.eclipse.emf.ecore</artifactId>
            <version>2.2.1.v200609210005</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.emf</groupId>
            <artifactId>org.eclipse.emf.common</artifactId>
            <version>2.2.1.v200609210005</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.emf</groupId>
            <artifactId>org.eclipse.emf.ecore.sdo</artifactId>
            <version>2.2.0.v200609210005</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.emf</groupId>
            <artifactId>org.eclipse.emf.ecore.xmi</artifactId>
            <version>2.2.1.v200609210005</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.emf</groupId>
            <artifactId>org.eclipse.emf.ecore.change</artifactId>
            <version>2.2.0.v200609210005</version>
        </dependency>

        <dependency>
            <groupId>intact.commons.capi</groupId>
            <artifactId>common-api-segmentation</artifactId>
            <version>3.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.commons.datamediator</groupId>
            <artifactId>datamediator-com-pl</artifactId>
            <version>${datamediator-com-pl.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.commons.moneris</groupId>
            <artifactId>moneris-api</artifactId>
            <version>${moneris.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>jdom</artifactId>
                    <groupId>org.jdom</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom2</artifactId>
            <version>2.0.6.1</version>
        </dependency>

        <dependency>
            <groupId>intact.commons.abr</groupId>
            <artifactId>abr</artifactId>
            <version>${abr.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-joda</artifactId>
            <version>${jackson-datatype.version}</version>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-client</artifactId>
            <version>3.1.10</version>
        </dependency>

        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-client</artifactId>
            <version>1.19.5-atlassian-17</version>
            <exclusions>
                <exclusion>
                    <groupId>org.codehaus.jettison</groupId>
                    <artifactId>jettison</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-core</artifactId>
            <version>1.19.5-atlassian-17</version>
        </dependency>
        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-server</artifactId>
            <version>1.19.4</version>
        </dependency>

        <dependency>
            <groupId>intact.commons.utility</groupId>
            <artifactId>security-tools</artifactId>
            <version>${security-tools.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.web.autoquote</groupId>
            <artifactId>autoquote-intact-resources</artifactId>
            <version>${autoquote-intact-resources.version}</version>
        </dependency>

        <dependency>
            <groupId>intact.commons.capi</groupId>
            <artifactId>common-api-webmethods</artifactId>
            <version>${common-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.commons.capi</groupId>
            <artifactId>common-api-web-security</artifactId>
            <version>${common-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xercesImpl</artifactId>
                    <groupId>xerces</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-httpclient</artifactId>
                    <groupId>commons-httpclient</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.commons.bam</groupId>
            <artifactId>access-manager-api</artifactId>
            <version>${access-manager.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-lang</groupId>
                    <artifactId>commons-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.intactfc.bloom</groupId>
            <artifactId>bloom-mq-handler-client</artifactId>
            <version>1.3.35</version>
        </dependency>

        <dependency>
            <groupId>intact.commons.rating-simplification</groupId>
            <artifactId>rating-simplification-core</artifactId>
            <version>${rating-simplification.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.owasp.esapi</groupId>
                    <artifactId>esapi</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>intact.commons.rating-simplification</groupId>
            <artifactId>rating-simplification-intact</artifactId>
            <version>${rating-simplification.version}</version>
        </dependency>

        <dependency>
            <groupId>intact.commons.service.glb</groupId>
            <artifactId>persist-glb-services</artifactId>
            <version>${glb.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <!-- This configuration layout is to be able to use an external classpath folder. -->
                    <layout>ZIP</layout>
                    <arguments>--spring.profiles.active=local</arguments>
                </configuration>
                <executions>
                    <execution>
                        <id>build-info</id>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-maven-plugin</artifactId>
                <version>1.5</version>
                <configuration>
                    <apiDocsUrl>http://localhost:8080/v3/api-docs.yaml</apiDocsUrl>
                    <outputFileName>autoquote-backend.yaml</outputFileName>
                    <outputDir>${project.parent.basedir}/autoquote-backend-contract/src/main/resources/definitions
                    </outputDir>
                    <skip>${skipITs}</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>integration-test</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${mavenJacocoPluginVersion}</version>
                <configuration>
                    <excludes>
                        <exclude>**/*DTO.*</exclude>
                        <exclude>**/*Application.*</exclude>
                        <exclude>**/*Config.*</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.pitest</groupId>
                <artifactId>pitest-maven</artifactId>
                <version>${org-pitest.version}</version>
                <configuration>
                    <excludedClasses>
                        <param>*Config</param>
                    </excludedClasses>
                    <outputFormats>
                        <format>HTML</format>
                    </outputFormats>
                    <timestampedReports>false</timestampedReports>
                    <historyInputFile>${pitest.historyFile}</historyInputFile>
                    <historyOutputFile>${pitest.historyFile}</historyOutputFile>
                </configuration>
                <dependencies>
                    <dependency>
                        <artifactId>pitest-junit5-plugin</artifactId>
                        <groupId>org.pitest</groupId>
                        <version>${org-pitest-junit.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>
